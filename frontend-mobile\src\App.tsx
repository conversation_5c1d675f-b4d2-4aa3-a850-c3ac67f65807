import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import MobileIndex from "./pages/mobile/MobileIndex";
import ContractsPage from "./pages/mobile/ContractsPage";
import ContractDetailPage from "./pages/mobile/ContractDetailPage";
import OpportunitiesPage from "./pages/mobile/OpportunitiesPage";
import OpportunityDetailPage from "./pages/mobile/OpportunityDetailPage";
import PaymentDetailPage from "./pages/mobile/PaymentDetailPage";
import PartnersPage from "./pages/mobile/PartnersPage";
import PartnerDetailPage from "./pages/mobile/PartnerDetailPage";
import ProfilePage from "./pages/mobile/ProfilePage";
import LoginPage from "./pages/mobile/LoginPage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<MobileIndex />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/contracts" element={<ContractsPage />} />
          <Route path="/contracts/:id" element={<ContractDetailPage />} />
          <Route path="/contracts/:contractId/payments/:paymentId" element={<PaymentDetailPage />} />
          <Route path="/opportunities" element={<OpportunitiesPage />} />
          <Route path="/opportunities/:id" element={<OpportunityDetailPage />} />
          <Route path="/partners" element={<PartnersPage />} />
          <Route path="/partners/:id" element={<PartnerDetailPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
