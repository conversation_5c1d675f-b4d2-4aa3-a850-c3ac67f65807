<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.contract.mapper.ContractPartnerRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hntsz.boot.modules.contract.model.entity.ContractPartnerRelation">
        <id column="id" property="id" />
        <result column="contract_id" property="contractId" />
        <result column="partner_id" property="partnerId" />
        <result column="partner_role" property="partnerRole" />
        <result column="partner_role_desc" property="partnerRoleDesc" />
        <result column="signing_person" property="signingPerson" />
        <result column="signing_person_title" property="signingPersonTitle" />
        <result column="signing_date" property="signingDate" />
        <result column="sort" property="sort" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_id, partner_id, partner_role, partner_role_desc, signing_person, 
        signing_person_title, signing_date, sort, remark, create_by, create_time, 
        update_by, update_time
    </sql>

    <!-- 根据合同ID获取伙伴关联信息 -->
    <select id="getByContractId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO">
        SELECT 
            cpr.id,
            cpr.contract_id,
            cpr.partner_id,
            p.partner_name,
            p.partner_code,
            p.partner_type,
            pt.label AS partner_type_label,
            cpr.partner_role,
            pr.label AS partner_role_label,
            cpr.partner_role_desc,
            prd.label AS partner_role_desc_label,
            cpr.signing_person,
            cpr.signing_person_title,
            cpr.signing_date,
            cpr.sort,
            cpr.remark,
            cpr.create_time,
            cpr.update_time
        FROM contract_partner_relation cpr
        LEFT JOIN partner p ON cpr.partner_id = p.id
        LEFT JOIN sys_dict_item pt ON p.partner_type = pt.value AND pt.dict_code = 'partner_type'
        LEFT JOIN sys_dict_item pr ON cpr.partner_role = pr.value AND pr.dict_code = 'partner_role'
        LEFT JOIN sys_dict_item prd ON cpr.partner_role_desc = prd.value AND prd.dict_code = 'partner_role_desc'
        WHERE cpr.contract_id = #{contractId}
        ORDER BY cpr.sort ASC, cpr.create_time ASC
    </select>

    <!-- 批量根据合同ID获取伙伴关联信息 -->
    <select id="getByContractIds" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO">
        SELECT 
            cpr.id,
            cpr.contract_id,
            cpr.partner_id,
            p.partner_name,
            p.partner_code,
            p.partner_type,
            pt.label AS partner_type_label,
            cpr.partner_role,
            pr.label AS partner_role_label,
            cpr.partner_role_desc,
            prd.label AS partner_role_desc_label,
            cpr.signing_person,
            cpr.signing_person_title,
            cpr.signing_date,
            cpr.sort,
            cpr.remark,
            cpr.create_time,
            cpr.update_time
        FROM contract_partner_relation cpr
        LEFT JOIN partner p ON cpr.partner_id = p.id
        LEFT JOIN sys_dict_item pt ON p.partner_type = pt.value AND pt.dict_code = 'partner_type'
        LEFT JOIN sys_dict_item pr ON cpr.partner_role = pr.value AND pr.dict_code = 'partner_role'
        LEFT JOIN sys_dict_item prd ON cpr.partner_role_desc = prd.value AND prd.dict_code = 'partner_role_desc'
        WHERE cpr.contract_id IN
        <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
            #{contractId}
        </foreach>
        ORDER BY cpr.contract_id ASC, cpr.sort ASC, cpr.create_time ASC
    </select>

</mapper>
