<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.contract.mapper.ContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hntsz.boot.modules.contract.model.entity.Contract">
        <id column="id" property="id" />
        <result column="contract_no" property="contractNo" />
        <result column="contract_name" property="contractName" />
        <result column="contract_type" property="contractType" />
        <result column="contract_category" property="contractCategory" />
        <result column="contract_amount" property="contractAmount" />
        <result column="signing_date" property="signingDate" />
        <result column="effective_date" property="effectiveDate" />
        <result column="expiry_date" property="expiryDate" />
        <result column="contract_status" property="contractStatus" />
        <result column="payment_method" property="paymentMethod" />
        <result column="attachment_id" property="attachmentId" />
        <result column="signing_location" property="signingLocation" />
        <result column="responsible_user_id" property="responsibleUserId" />
        <result column="dept_id" property="deptId" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_no, contract_name, contract_type, contract_category, contract_amount,
        opportunity_id, signing_date, effective_date, expiry_date, contract_status, payment_method,
        attachment_id, signing_location, responsible_user_id, dept_id, remark,
        create_by, create_time, update_by, update_time, is_deleted
    </sql>

    <!-- 获取合同分页列表 -->
    <select id="getContractPage" resultType="com.hntsz.boot.modules.contract.model.vo.ContractVO">
        SELECT
            c.id,
            c.contract_no,
            c.contract_name,
            c.contract_type,
            ct.label AS contract_type_label,
            c.contract_category,
            cc.label AS contract_category_label,
            c.contract_amount,
            c.opportunity_id,
            c.signing_date,
            c.effective_date,
            c.expiry_date,
            c.contract_status,
            c.payment_method,
            pm.label AS payment_method_label,
            c.signing_location,
            c.responsible_user_id,
            u.nickname AS responsible_user_name,
            c.dept_id,
            d.name AS dept_name,
            c.remark,
            (SELECT 
                COALESCE(SUM(
                    CASE 
                        WHEN payer.is_our_company = 1 THEN -cp.actual_amount
                        WHEN payee.is_our_company = 1 THEN cp.actual_amount
                        ELSE 0
                    END
                ), 0)
                FROM contract_payment cp
                LEFT JOIN partner payer ON cp.payer_partner_id = payer.id
                LEFT JOIN partner payee ON cp.payee_partner_id = payee.id
                WHERE cp.contract_id = c.id AND cp.is_deleted = 0 AND cp.payment_status = 'paid'
            ) AS total_payment_amount,
            c.create_time,
            c.update_time
        FROM contract c
        LEFT JOIN sys_dict_item ct ON c.contract_type = ct.value AND ct.dict_code = 'contract_type'
        LEFT JOIN sys_dict_item cc ON c.contract_category = cc.value AND cc.dict_code = 'contract_category'
        LEFT JOIN sys_dict_item pm ON c.payment_method = pm.value AND pm.dict_code = 'payment_method'
        LEFT JOIN sys_user u ON c.responsible_user_id = u.id
        LEFT JOIN sys_dept d ON c.dept_id = d.id
        <if test="query != null and query.partnerId != null">
            INNER JOIN contract_partner_relation cpr ON c.id = cpr.contract_id AND cpr.partner_id = #{query.partnerId}
        </if>
        WHERE c.is_deleted = 0
        <if test="query != null">
            <if test="query.keywords != null and query.keywords != ''">
                AND (c.contract_no LIKE CONCAT('%', #{query.keywords}, '%')
                    OR c.contract_name LIKE CONCAT('%', #{query.keywords}, '%'))
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                AND c.contract_type = #{query.contractType}
            </if>
            <if test="query.contractCategory != null and query.contractCategory != ''">
                AND c.contract_category = #{query.contractCategory}
            </if>
            <if test="query.contractStatus != null and query.contractStatus != ''">
                AND c.contract_status = #{query.contractStatus}
            </if>
            <if test="query.responsibleUserId != null">
                AND c.responsible_user_id = #{query.responsibleUserId}
            </if>
            <if test="query.deptId != null">
                AND c.dept_id = #{query.deptId}
            </if>
            <if test="query.signingDateStart != null">
                AND c.signing_date &gt;= #{query.signingDateStart}
            </if>
            <if test="query.signingDateEnd != null">
                AND c.signing_date &lt;= #{query.signingDateEnd}
            </if>
            <if test="query.effectiveDateStart != null">
                AND c.effective_date &gt;= #{query.effectiveDateStart}
            </if>
            <if test="query.effectiveDateEnd != null">
                AND c.effective_date &lt;= #{query.effectiveDateEnd}
            </if>
            <if test="query.expiryDateStart != null">
                AND c.expiry_date &gt;= #{query.expiryDateStart}
            </if>
            <if test="query.expiryDateEnd != null">
                AND c.expiry_date &lt;= #{query.expiryDateEnd}
            </if>
            <if test="query.contractAmountMin != null">
                AND c.contract_amount &gt;= #{query.contractAmountMin}
            </if>
            <if test="query.contractAmountMax != null">
                AND c.contract_amount &lt;= #{query.contractAmountMax}
            </if>
            <if test="query.opportunityId != null">
                AND c.opportunity_id = #{query.opportunityId}
            </if>
        </if>
        ORDER BY c.create_time DESC
    </select>

    <!-- 获取合同详情 -->
    <select id="getContractDetail" resultType="com.hntsz.boot.modules.contract.model.vo.ContractVO">
        SELECT
            c.id,
            c.contract_no,
            c.contract_name,
            c.contract_type,
            ct.label AS contract_type_label,
            c.contract_category,
            cc.label AS contract_category_label,
            c.contract_amount,
            c.opportunity_id,
            c.signing_date,
            c.effective_date,
            c.expiry_date,
            c.contract_status,
            c.payment_method,
            pm.label AS payment_method_label,
            c.signing_location,
            c.responsible_user_id,
            u.nickname AS responsible_user_name,
            c.dept_id,
            d.name AS dept_name,
            c.remark,
            c.create_time,
            c.update_time
        FROM contract c
        LEFT JOIN sys_dict_item ct ON c.contract_type = ct.value AND ct.dict_code = 'contract_type'
        LEFT JOIN sys_dict_item cc ON c.contract_category = cc.value AND cc.dict_code = 'contract_category'
        LEFT JOIN sys_dict_item pm ON c.payment_method = pm.value AND pm.dict_code = 'payment_method'
        LEFT JOIN sys_user u ON c.responsible_user_id = u.id
        LEFT JOIN sys_dept d ON c.dept_id = d.id
        WHERE c.id = #{id} AND c.is_deleted = 0
    </select>

    <!-- 根据合同编号查询合同 -->
    <select id="getByContractNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM contract
        WHERE contract_no = #{contractNo} AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 获取合同选项列表 -->
    <select id="getContractOptions" resultType="com.hntsz.boot.modules.contract.model.vo.ContractVO">
        SELECT 
            id,
            contract_no,
            contract_name,
            contract_type,
            contract_status,
            contract_amount
        FROM contract
        WHERE is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据伙伴ID获取相关合同列表 -->
    <select id="getContractsByPartnerId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractVO">
        SELECT 
            c.id,
            c.contract_no,
            c.contract_name,
            c.contract_type,
            ct.label AS contract_type_label,
            c.contract_amount,
            c.signing_date,
            c.contract_status,
            cpr.partner_role,
            pr.label AS partner_role_label
        FROM contract c
        INNER JOIN contract_partner_relation cpr ON c.id = cpr.contract_id
        LEFT JOIN sys_dict_item ct ON c.contract_type = ct.value AND ct.dict_code = 'contract_type'
        LEFT JOIN sys_dict_item pr ON cpr.partner_role = pr.value AND pr.dict_code = 'partner_role'
        WHERE cpr.partner_id = #{partnerId} AND c.is_deleted = 0
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据负责人ID获取合同列表 -->
    <select id="getContractsByResponsibleUserId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractVO">
        SELECT 
            c.id,
            c.contract_no,
            c.contract_name,
            c.contract_type,
            ct.label AS contract_type_label,
            c.contract_amount,
            c.signing_date,
            c.contract_status
        FROM contract c
        LEFT JOIN sys_dict_item ct ON c.contract_type = ct.value AND ct.dict_code = 'contract_type'
        WHERE c.responsible_user_id = #{responsibleUserId} AND c.is_deleted = 0
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据部门ID获取合同列表 -->
    <select id="getContractsByDeptId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractVO">
        SELECT 
            c.id,
            c.contract_no,
            c.contract_name,
            c.contract_type,
            ct.label AS contract_type_label,
            c.contract_amount,
            c.signing_date,
            c.contract_status
        FROM contract c
        LEFT JOIN sys_dict_item ct ON c.contract_type = ct.value AND ct.dict_code = 'contract_type'
        WHERE c.dept_id = #{deptId} AND c.is_deleted = 0
        ORDER BY c.create_time DESC
    </select>

</mapper>
