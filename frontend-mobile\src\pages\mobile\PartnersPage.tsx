import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Filter, 
  Plus,
  Building,
  Phone,
  Mail,
  MapPin,
  Star
} from "lucide-react";

export default function PartnersPage() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");

  const partners = [
    {
      id: 1,
      name: "阿里云计算有限公司",
      code: "PT001",
      type: "supplier",
      typeText: "供应商",
      contactPerson: "张经理",
      contactPhone: "138****8888",
      contactEmail: "<EMAIL>",
      address: "杭州市余杭区",
      status: "active",
      isStarred: true,
      cooperationYears: 3
    },
    {
      id: 2,
      name: "腾讯云计算有限公司", 
      code: "PT002",
      type: "customer",
      typeText: "客户",
      contactPerson: "李总监",
      contactPhone: "139****9999",
      contactEmail: "<EMAIL>",
      address: "深圳市南山区",
      status: "active",
      isStarred: false,
      cooperationYears: 2
    },
    {
      id: 3,
      name: "华为技术有限公司",
      code: "PT003", 
      type: "partner",
      typeText: "合作伙伴",
      contactPerson: "王主任",
      contactPhone: "136****7777",
      contactEmail: "<EMAIL>",
      address: "深圳市龙岗区",
      status: "active",
      isStarred: true,
      cooperationYears: 5
    },
    {
      id: 4,
      name: "百度在线网络技术公司",
      code: "PT004",
      type: "supplier",
      typeText: "供应商", 
      contactPerson: "刘经理",
      contactPhone: "135****6666",
      contactEmail: "<EMAIL>",
      address: "北京市海淀区",
      status: "inactive",
      isStarred: false,
      cooperationYears: 1
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case "customer": return "success";
      case "supplier": return "primary";
      case "partner": return "accent";
      default: return "secondary";
    }
  };

  const filteredPartners = partners.filter(partner => {
    const matchesSearch = partner.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         partner.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         partner.contactPerson.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = filterType === "all" || partner.type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-background pb-20">
      <MobileHeader 
        title="伙伴管理" 
        showSearch={true}
        searchPlaceholder="搜索伙伴..."
        onSearch={(query) => setSearchQuery(query)}
        searchValue={searchQuery}
      />
      
      <div className="p-4 space-y-4">

        {/* 伙伴列表 */}
        <div className="space-y-3">
          {filteredPartners.map((partner) => (
            <div 
              key={partner.id} 
              className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate(`/partners/${partner.id}`)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Building className="h-4 w-4 text-primary" />
                    <h3 className="font-semibold text-sm">{partner.name}</h3>
                    {partner.isStarred && (
                      <Star className="h-3 w-3 text-warning fill-current" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mb-1">
                    编号: {partner.code}
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  <Badge variant={getTypeColor(partner.type) as any} className="text-xs">
                    {partner.typeText}
                  </Badge>
                  <Badge 
                    variant={partner.status === "active" ? "default" : "secondary"} 
                    className="text-xs"
                  >
                    {partner.status === "active" ? "正常" : "禁用"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{partner.contactPerson}</span>
                  <span className="text-muted-foreground">{partner.contactPhone}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{partner.contactEmail}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{partner.address}</span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">合作年限</span>
                  <span className="font-medium text-primary">{partner.cooperationYears}年</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredPartners.length === 0 && (
          <div className="text-center py-8">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">未找到相关伙伴</p>
          </div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
}