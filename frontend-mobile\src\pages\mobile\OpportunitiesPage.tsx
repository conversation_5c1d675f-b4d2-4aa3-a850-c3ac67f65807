import { useState } from "react";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import { 
  Search, 
  Filter, 
  Plus,
  Target,
  Building,
  User,
  DollarSign,
  Calendar,
  TrendingUp
} from "lucide-react";

export default function OpportunitiesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  const opportunities = [
    {
      id: 1,
      code: "OP2024001",
      name: "企业数字化转型方案",
      stage: "proposal",
      stageText: "方案阶段",
      probability: 75,
      partner: "中国移动通信集团",
      contactPerson: "李经理",
      estimatedAmount: "¥1,200,000",
      estimatedCloseDate: "2024-03-15",
      priority: "high",
      status: "active"
    },
    {
      id: 2,
      code: "OP2024002",
      name: "云计算基础设施服务",
      stage: "negotiation", 
      stageText: "谈判阶段",
      probability: 60,
      partner: "招商银行股份有限公司",
      contactPerson: "王总监",
      estimatedAmount: "¥800,000",
      estimatedCloseDate: "2024-02-28",
      priority: "medium",
      status: "active"
    },
    {
      id: 3,
      code: "OP2024003",
      name: "智能数据分析平台",
      stage: "interested",
      stageText: "有意向",
      probability: 40,
      partner: "平安保险集团",
      contactPerson: "张主任",
      estimatedAmount: "¥950,000",
      estimatedCloseDate: "2024-04-20",
      priority: "low",
      status: "active"
    }
  ];

  const getStageColor = (stage: string) => {
    switch (stage) {
      case "initial": return "secondary";
      case "interested": return "warning"; 
      case "proposal": return "accent";
      case "negotiation": return "primary";
      case "closed_won": return "success";
      case "closed_lost": return "destructive";
      default: return "secondary";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "destructive";
      case "medium": return "warning";
      case "low": return "secondary";
      default: return "secondary";
    }
  };

  const filteredOpportunities = opportunities.filter(opp =>
    opp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    opp.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
    opp.partner.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-background pb-20">
      <MobileHeader 
        title="商机管理" 
        showSearch={true}
        searchPlaceholder="搜索商机..."
        onSearch={(query) => setSearchQuery(query)}
        searchValue={searchQuery}
      />
      
      <div className="p-4 space-y-4">
        {/* 统计卡片 */}
        <div className="grid grid-cols-3 gap-2">
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-primary">56</p>
            <p className="text-xs text-muted-foreground">总数</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-success">12</p>
            <p className="text-xs text-muted-foreground">成交</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-warning">28</p>
            <p className="text-xs text-muted-foreground">跟进中</p>
          </div>
        </div>

        {/* 商机列表 */}
        <div className="space-y-3">
          {filteredOpportunities.map((opportunity) => (
            <div 
              key={opportunity.id} 
              className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => {
                navigate(`/opportunities/${opportunity.id}`);
              }}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Target className="h-4 w-4 text-primary" />
                    <h3 className="font-semibold text-sm">{opportunity.name}</h3>
                  </div>
                  <p className="text-xs text-muted-foreground mb-1">
                    编号: {opportunity.code}
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  <Badge variant={getStageColor(opportunity.stage) as any} className="text-xs">
                    {opportunity.stageText}
                  </Badge>
                  <Badge variant={getPriorityColor(opportunity.priority) as any} className="text-xs">
                    {opportunity.priority === "high" ? "高" : opportunity.priority === "medium" ? "中" : "低"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Building className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{opportunity.partner}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{opportunity.contactPerson}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <DollarSign className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium text-primary">{opportunity.estimatedAmount}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">预计成交: {opportunity.estimatedCloseDate}</span>
                </div>

                {/* 成单概率 */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      成单概率
                    </span>
                    <span className="font-medium">{opportunity.probability}%</span>
                  </div>
                  <Progress value={opportunity.probability} className="h-2" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredOpportunities.length === 0 && (
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">未找到相关商机</p>
          </div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
}