package com.hntsz.boot.modules.opportunity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.modules.opportunity.model.entity.Opportunity;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityForm;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO;

import java.util.List;

/**
 * 商机线索服务接口
 *
 * <AUTHOR>
 */
public interface OpportunityService extends IService<Opportunity> {

    /**
     * 分页查询商机线索
     *
     * @param queryParams 查询参数
     * @return 商机线索分页列表
     */
    PageResult<OpportunityVO> getOpportunityPage(OpportunityQuery queryParams);

    /**
     * 根据ID查询商机线索详情
     *
     * @param id 商机ID
     * @return 商机线索详情
     */
    OpportunityVO getOpportunityById(Long id);

    /**
     * 根据ID获取商机表单数据
     *
     * @param id 商机ID
     * @return 商机表单数据
     */
    OpportunityForm getOpportunityFormData(Long id);

    /**
     * 新增商机线索
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveOpportunity(OpportunityForm formData);

    /**
     * 更新商机线索
     *
     * @param id       商机ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateOpportunity(Long id, OpportunityForm formData);

    /**
     * 删除商机线索
     *
     * @param ids 商机ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteOpportunities(String ids);

    /**
     * 获取商机线索选项列表
     *
     * @return 商机线索选项列表
     */
    List<Option<Long>> getOpportunityOptions();

    /**
     * 生成商机编码
     *
     * @return 商机编码
     */
    String generateOpportunityCode();

    /**
     * 批量转移商机负责人
     *
     * @param opportunityIds    商机ID列表
     * @param responsibleUserId 新负责人ID
     * @return 是否成功
     */
    boolean transferOpportunities(List<Long> opportunityIds, Long responsibleUserId);

    /**
     * 更新商机阶段
     *
     * @param id                商机ID
     * @param opportunityStage  新阶段
     * @param winProbability    成单概率
     * @return 是否成功
     */
    boolean updateOpportunityStage(Long id, String opportunityStage, Integer winProbability);

    /**
     * 成交商机
     *
     * @param id 商机ID
     * @return 是否成功
     */
    boolean winOpportunity(Long id);

    /**
     * 失败商机
     *
     * @param id         商机ID
     * @param lostReason 失败原因
     * @return 是否成功
     */
    boolean loseOpportunity(Long id, String lostReason);

    /**
     * 归档商机线索
     *
     * @param ids 商机ID，多个以英文逗号(,)分割
     * @param archiveReason 归档原因
     * @return 是否成功
     */
    boolean archiveOpportunities(String ids, String archiveReason);
}