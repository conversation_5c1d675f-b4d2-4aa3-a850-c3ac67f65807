package com.hntsz.boot.modules.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntsz.boot.modules.contract.model.entity.ContractPartnerRelation;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同伙伴关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface ContractPartnerRelationMapper extends BaseMapper<ContractPartnerRelation> {

    /**
     * 根据合同ID获取伙伴关联信息
     *
     * @param contractId 合同ID
     * @return 伙伴关联信息列表
     */
    List<ContractPartnerRelationVO> getByContractId(@Param("contractId") Long contractId);

    /**
     * 批量根据合同ID获取伙伴关联信息
     *
     * @param contractIds 合同ID列表
     * @return 伙伴关联信息列表
     */
    List<ContractPartnerRelationVO> getByContractIds(@Param("contractIds") List<Long> contractIds);
}
