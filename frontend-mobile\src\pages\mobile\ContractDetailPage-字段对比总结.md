# 合同详情页面接口联调 - 字段对比总结

## 概述
已完成移动端合同详情页面的接口联调，将静态数据替换为真实API调用。以下是字段对比和处理方案的详细说明。

## 主要接口

### 1. 合同详情接口
- **接口**: `GET /api/v1/contracts/{id}`
- **状态**: ✅ 已联调
- **返回类型**: `ContractVO`

### 2. 合同付款记录接口
- **接口**: `GET /api/v1/contract-payments/by-contract/{contractId}`
- **状态**: ✅ 已联调
- **返回类型**: `ContractPaymentVO[]`

### 3. 合同附件接口
- **接口**: `GET /api/v1/contracts/{contractId}/attachments`
- **状态**: ✅ 已联调
- **返回类型**: `ContractAttachmentVO[]`

## 字段对比和处理方案

### 合同基本信息 (ContractVO)

| 移动端期望字段 | PC端实际字段 | 处理方案 | 状态 |
|---------------|-------------|----------|------|
| `contractStatusText` | `contractStatus` | 直接使用 `contractStatus` | ✅ 已修复 |
| `contractTypeText` | `contractTypeLabel` | 使用 `contractTypeLabel \|\| contractType` | ✅ 已修复 |
| `contractCategoryText` | `contractCategoryLabel` | 使用 `contractCategoryLabel \|\| contractCategory` | ✅ 已修复 |
| `contractAmount` (格式化字符串) | `contractAmount` (数字) | 添加 `formatAmount()` 函数处理 | ✅ 已修复 |
| `paymentMethodText` | `paymentMethodLabel` | 使用 `paymentMethodLabel \|\| paymentMethod` | ✅ 已修复 |
| `opportunityName` | 不存在 | 显示 `opportunityId` | ⚠️ 需要额外接口获取商机名称 |

### 合同伙伴关系 (ContractPartnerRelationVO)

| 移动端期望字段 | PC端实际字段 | 处理方案 | 状态 |
|---------------|-------------|----------|------|
| `partnerRoleText` | `partnerRoleLabel` | 使用 `partnerRoleLabel \|\| partnerRole` | ✅ 已修复 |
| `partnerRoleDescText` | `partnerRoleDescLabel` | 使用 `partnerRoleDescLabel \|\| partnerRoleDesc` | ✅ 已修复 |
| `contactPerson` | 不存在 | 显示提示信息 | ⚠️ 需要通过伙伴详情接口获取 |
| `contactPhone` | 不存在 | 显示提示信息 | ⚠️ 需要通过伙伴详情接口获取 |
| `contactEmail` | 不存在 | 显示提示信息 | ⚠️ 需要通过伙伴详情接口获取 |

### 付款记录 (ContractPaymentVO)

| 移动端期望字段 | PC端实际字段 | 处理方案 | 状态 |
|---------------|-------------|----------|------|
| `paymentStatusText` | `paymentStatusLabel` | 使用 `paymentStatusLabel \|\| paymentStatus` | ✅ 已修复 |
| `paymentTypeText` | `paymentTypeLabel` | 使用 `paymentTypeLabel \|\| paymentType` | ✅ 已修复 |
| `paymentMethodText` | `paymentMethodLabel` | 使用 `paymentMethodLabel \|\| paymentMethod` | ✅ 已修复 |
| `invoiceStatusText` | `invoiceStatusLabel` | 使用 `invoiceStatusLabel \|\| invoiceStatus` | ✅ 已修复 |
| `actualAmount` (格式化字符串) | `actualAmount` (数字) | 使用 `formatAmount()` 函数处理 | ✅ 已修复 |
| `plannedAmount` (格式化字符串) | `plannedAmount` (数字) | 使用 `formatAmount()` 函数处理 | ✅ 已修复 |

### 合同附件 (ContractAttachmentVO)

| 移动端期望字段 | PC端实际字段 | 处理方案 | 状态 |
|---------------|-------------|----------|------|
| `fileName` | `attachmentName` | 使用 `attachmentName` | ✅ 已修复 |
| `fileType` | `attachmentType` | 使用 `attachmentType` | ✅ 已修复 |
| `fileSize` | `attachmentSize` | 使用 `attachmentSize` | ✅ 已修复 |
| `filePath` | `attachmentPath/attachmentUrl` | 使用 `attachmentUrl \|\| attachmentPath` | ✅ 已修复 |

## 数据类型差异

### ID类型
- **问题**: PC端使用 `string` 类型ID，移动端使用 `number` 类型
- **处理**: 移动端API已统一使用 `number` 类型，与后端保持一致

### 金额格式
- **问题**: PC端返回 `number` 类型，移动端期望格式化字符串
- **处理**: 添加 `formatAmount()` 函数进行格式化

## 需要注意的问题

### 1. 商机名称缺失
- **问题**: 合同详情中只有 `opportunityId`，没有商机名称
- **建议**: 需要调用商机详情接口获取商机名称，或在合同详情接口中包含商机名称

### 2. 伙伴联系信息缺失
- **问题**: 合同伙伴关系中没有联系人信息
- **建议**: 需要调用伙伴详情接口获取联系信息，或在合同详情接口中包含伙伴联系信息

### 3. Badge组件variant限制
- **问题**: Badge组件不支持 "primary" variant
- **处理**: 改用 "default" variant

## 实现的功能

1. ✅ 合同基本信息展示
2. ✅ 合同金额格式化显示
3. ✅ 合同状态、类型、分类标签显示
4. ✅ 付款方式显示
5. ✅ 签署地点、负责人信息显示
6. ✅ 合同伙伴关系展示（基本信息）
7. ✅ 付款记录列表展示
8. ✅ 合同附件列表展示
9. ✅ 加载状态和错误处理
10. ✅ 响应式设计适配

## 后续优化建议

1. **增强商机信息**: 在合同详情接口中包含商机名称
2. **完善伙伴信息**: 在合同伙伴关系中包含联系信息，或提供快速获取伙伴详情的方法
3. **缓存优化**: 对频繁访问的数据进行缓存
4. **错误处理**: 增加更详细的错误提示和重试机制
5. **性能优化**: 考虑分页加载大量付款记录和附件
