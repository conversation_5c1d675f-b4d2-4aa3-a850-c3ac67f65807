package com.hntsz.boot.modules.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.contract.model.form.ContractPaymentForm;
import com.hntsz.boot.modules.contract.model.form.PaymentAttachmentForm;
import com.hntsz.boot.modules.contract.model.query.ContractPaymentQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO;
import com.hntsz.boot.modules.contract.model.vo.PaymentAttachmentVO;
import com.hntsz.boot.modules.contract.service.ContractPaymentService;
import com.hntsz.boot.modules.contract.service.PaymentAttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同付款记录控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Tag(name = "合同付款记录接口")
@RestController
@RequestMapping("/api/v1/contract-payments")
@RequiredArgsConstructor
public class ContractPaymentController {

    private final ContractPaymentService contractPaymentService;
    private final PaymentAttachmentService paymentAttachmentService;

    @Operation(summary = "合同付款记录分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPerm('contract:payment:query')")
    public PageResult<ContractPaymentVO> getContractPaymentPage(ContractPaymentQuery queryParams) {
        IPage<ContractPaymentVO> result = contractPaymentService.getContractPaymentPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "合同付款记录详情")
    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('contract:payment:query')")
    public Result<ContractPaymentVO> getContractPaymentDetail(
            @Parameter(description = "付款记录ID") @PathVariable Long id) {
        ContractPaymentVO contractPayment = contractPaymentService.getContractPaymentDetail(id);
        return Result.success(contractPayment);
    }

    @Operation(summary = "获取合同付款记录表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('contract:payment:query')")
    public Result<ContractPaymentForm> getContractPaymentFormData(
            @Parameter(description = "付款记录ID") @PathVariable Long id) {
        ContractPaymentForm formData = contractPaymentService.getContractPaymentFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "新增合同付款记录")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('contract:payment:add')")
    public Result<Void> saveContractPayment(@Validated @RequestBody ContractPaymentForm form) {
        boolean result = contractPaymentService.saveContractPayment(form);
        return Result.judge(result);
    }

    @Operation(summary = "修改合同付款记录")
    @PutMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('contract:payment:edit')")
    public Result<Void> updateContractPayment(
            @Parameter(description = "付款记录ID") @PathVariable Long id,
            @Validated @RequestBody ContractPaymentForm form) {
        boolean result = contractPaymentService.updateContractPayment(id, form);
        return Result.judge(result);
    }

    @Operation(summary = "删除合同付款记录")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('contract:payment:delete')")
    public Result<Void> deleteContractPayments(
            @Parameter(description = "付款记录ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        String[] idArray = ids.split(",");
        Long[] idLongArray = new Long[idArray.length];
        for (int i = 0; i < idArray.length; i++) {
            idLongArray[i] = Long.parseLong(idArray[i]);
        }
        boolean result = contractPaymentService.deleteContractPayments(idLongArray);
        return Result.judge(result);
    }

    @Operation(summary = "根据合同ID获取付款记录列表")
    @GetMapping("/by-contract/{contractId}")
    @PreAuthorize("@ss.hasPerm('contract:payment:query')")
    public Result<List<ContractPaymentVO>> getByContractId(
            @Parameter(description = "合同ID") @PathVariable Long contractId) {
        List<ContractPaymentVO> payments = contractPaymentService.getByContractId(contractId);
        return Result.success(payments);
    }

    @Operation(summary = "根据合同ID统计付款总额")
    @GetMapping("/total-amount/{contractId}")
    @PreAuthorize("@ss.hasPerm('contract:payment:query')")
    public Result<BigDecimal> getTotalAmountByContractId(
            @Parameter(description = "合同ID") @PathVariable Long contractId) {
        BigDecimal totalAmount = contractPaymentService.getTotalAmountByContractId(contractId);
        return Result.success(totalAmount);
    }

    @Operation(summary = "更新付款状态")
    @PutMapping("/{id}/payment-status")
    @PreAuthorize("@ss.hasPerm('contract:payment:edit')")
    public Result<Void> updatePaymentStatus(
            @Parameter(description = "付款记录ID") @PathVariable Long id,
            @Parameter(description = "付款状态") @RequestParam String status) {
        boolean result = contractPaymentService.updatePaymentStatus(id, status);
        return Result.judge(result);
    }

    @Operation(summary = "更新发票状态")
    @PutMapping("/{id}/invoice-status")
    @PreAuthorize("@ss.hasPerm('contract:payment:edit')")
    public Result<Void> updateInvoiceStatus(
            @Parameter(description = "付款记录ID") @PathVariable Long id,
            @Parameter(description = "发票状态") @RequestParam String invoiceStatus) {
        boolean result = contractPaymentService.updateInvoiceStatus(id, invoiceStatus);
        return Result.judge(result);
    }

    // ======================== 付款文件管理接口 ========================

    @Operation(summary = "获取付款文件列表")
    @GetMapping("/{paymentId}/attachments")
    @PreAuthorize("@ss.hasPerm('contract:payment:query')")
    public Result<List<PaymentAttachmentVO>> getPaymentAttachments(
            @Parameter(description = "付款记录ID") @PathVariable Long paymentId) {
        List<PaymentAttachmentVO> attachments = paymentAttachmentService.getByPaymentId(paymentId);
        return Result.success(attachments);
    }

    @Operation(summary = "添加付款文件")
    @PostMapping("/{paymentId}/attachments")
    @PreAuthorize("@ss.hasPerm('contract:payment:edit')")
    public Result<Void> addPaymentAttachment(
            @Parameter(description = "付款记录ID") @PathVariable Long paymentId,
            @Validated @RequestBody PaymentAttachmentForm form) {
        form.setPaymentId(paymentId);
        boolean result = paymentAttachmentService.savePaymentAttachment(form);
        return Result.judge(result);
    }

    @Operation(summary = "批量添加付款文件")
    @PostMapping("/{paymentId}/attachments/batch")
    @PreAuthorize("@ss.hasPerm('contract:payment:edit')")
    public Result<Void> batchAddPaymentAttachments(
            @Parameter(description = "付款记录ID") @PathVariable Long paymentId,
            @RequestBody List<Long> attachmentIds) {
        boolean result = paymentAttachmentService.batchSavePaymentAttachments(paymentId, attachmentIds);
        return Result.judge(result);
    }

    @Operation(summary = "删除付款文件")
    @DeleteMapping("/{paymentId}/attachments/{attachmentId}")
    @PreAuthorize("@ss.hasPerm('contract:payment:edit')")
    public Result<Void> deletePaymentAttachment(
            @Parameter(description = "付款记录ID") @PathVariable Long paymentId,
            @Parameter(description = "附件ID") @PathVariable Long attachmentId) {
        boolean result = paymentAttachmentService.deletePaymentAttachment(paymentId, attachmentId);
        return Result.judge(result);
    }
}
