import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Building2, User, Phone, Mail, MapPin, FileText, CreditCard, Landmark } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { MobileHeader } from '@/components/mobile/MobileHeader';

interface Partner {
  id: number;
  partner_name: string;
  partner_code: string;
  is_our_company: boolean;
  partner_type: string;
  legal_representative: string;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
  address: string;
  certificate_type: string;
  certificate_number: string;
  tax_number: string;
  bank_name: string;
  bank_account: string;
  status: string;
  remark: string;
  create_time: string;
  update_time: string;
}

// Mock data - replace with actual API call
const mockPartner: Partner = {
  id: 1,
  partner_name: "上海科技有限公司",
  partner_code: "********",
  is_our_company: false,
  partner_type: "customer",
  legal_representative: "张三",
  contact_person: "李四",
  contact_phone: "***********",
  contact_email: "<EMAIL>",
  address: "上海市浦东新区科技园路123号",
  certificate_type: "business_license",
  certificate_number: "91310000MA1FL2345X",
  tax_number: "91310000MA1FL2345X",
  bank_name: "中国银行上海分行",
  bank_account: "1234567890123456789",
  status: "active",
  remark: "重要合作伙伴，优先级高",
  create_time: "2024-01-15T10:30:00",
  update_time: "2024-07-20T14:20:00"
};

const getPartnerTypeLabel = (type: string) => {
  const types: { [key: string]: string } = {
    'customer': '客户',
    'supplier': '供应商',
    'partner': '合作伙伴',
    'other': '其他'
  };
  return types[type] || type;
};

const getPartnerTypeColor = (type: string) => {
  const colors: { [key: string]: string } = {
    'customer': 'bg-blue-100 text-blue-800',
    'supplier': 'bg-green-100 text-green-800',
    'partner': 'bg-purple-100 text-purple-800',
    'other': 'bg-gray-100 text-gray-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getStatusLabel = (status: string) => {
  return status === 'active' ? '正常' : '禁用';
};

const getStatusColor = (status: string) => {
  return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
};

const getCertificateTypeLabel = (type: string) => {
  const types: { [key: string]: string } = {
    'business_license': '营业执照',
    'organization_code': '组织机构代码',
    'tax_registration': '税务登记证',
    'other': '其他'
  };
  return types[type] || type;
};

const PartnerDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // In real app, fetch partner data based on id
  const partner = mockPartner;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div className="min-h-screen bg-background">
      <MobileHeader 
        title="伙伴详情" 
        showBack 
        onBack={() => navigate(-1)}
        showLogo={false}
      />

      <div className="p-4 space-y-6">
        {/* 基本信息卡片 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-xl font-semibold text-foreground mb-2">
                {partner.partner_name}
              </h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                <span>伙伴编码: {partner.partner_code}</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getPartnerTypeColor(partner.partner_type)}>
                  {getPartnerTypeLabel(partner.partner_type)}
                </Badge>
                <Badge className={getStatusColor(partner.status)}>
                  {getStatusLabel(partner.status)}
                </Badge>
                {partner.is_our_company && (
                  <Badge className="bg-amber-100 text-amber-800">
                    我司企业
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 联系信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <User className="w-5 h-5" />
            联系信息
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Building2 className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">法定代表人</div>
                <div className="text-foreground">{partner.legal_representative || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <User className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">联系人</div>
                <div className="text-foreground">{partner.contact_person || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">联系电话</div>
                <div className="text-foreground">{partner.contact_phone || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">联系邮箱</div>
                <div className="text-foreground">{partner.contact_email || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">地址</div>
                <div className="text-foreground">{partner.address || '暂无'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 证件信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <FileText className="w-5 h-5" />
            证件信息
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">证件类型</div>
                <div className="text-foreground">{getCertificateTypeLabel(partner.certificate_type)}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <FileText className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">证件号码</div>
                <div className="text-foreground">{partner.certificate_number || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <FileText className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">税号</div>
                <div className="text-foreground">{partner.tax_number || '暂无'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 银行信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <Landmark className="w-5 h-5" />
            银行信息
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Landmark className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">开户银行</div>
                <div className="text-foreground">{partner.bank_name || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">银行账号</div>
                <div className="text-foreground">{partner.bank_account || '暂无'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 备注信息 */}
        {partner.remark && (
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h2 className="text-lg font-semibold text-foreground mb-4">备注</h2>
            <div className="text-muted-foreground">{partner.remark}</div>
          </div>
        )}

      </div>
    </div>
  );
};

export default PartnerDetailPage;