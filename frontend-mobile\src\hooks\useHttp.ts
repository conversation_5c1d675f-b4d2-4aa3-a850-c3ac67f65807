import { useQuery, useMutation, useQueryClient, type UseQueryOptions, type UseMutationOptions } from '@tanstack/react-query';
import request from '@/utils/request';
import type { AxiosRequestConfig } from 'axios';

/**
 * HTTP请求Hook，基于React Query和Axios
 */

/**
 * GET请求Hook
 * @param key 查询键
 * @param url 请求URL
 * @param config axios配置
 * @param options React Query选项
 * @returns useQuery结果
 */
export function useHttpGet<TData = any>(
  key: string[],
  url: string,
  config?: AxiosRequestConfig,
  options?: Omit<UseQueryOptions<TData>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: key,
    queryFn: () => request.get<any, TData>(url, config),
    ...options,
  });
}

/**
 * POST请求Hook
 * @param options React Query mutation选项
 * @returns useMutation结果
 */
export function useHttpPost<TData = any, TVariables = any>(
  options?: UseMutationOptions<TData, Error, { url: string; data?: TVariables; config?: AxiosRequestConfig }>
) {
  return useMutation({
    mutationFn: ({ url, data, config }) => request.post<any, TData>(url, data, config),
    ...options,
  });
}

/**
 * PUT请求Hook
 * @param options React Query mutation选项
 * @returns useMutation结果
 */
export function useHttpPut<TData = any, TVariables = any>(
  options?: UseMutationOptions<TData, Error, { url: string; data?: TVariables; config?: AxiosRequestConfig }>
) {
  return useMutation({
    mutationFn: ({ url, data, config }) => request.put<any, TData>(url, data, config),
    ...options,
  });
}

/**
 * DELETE请求Hook
 * @param options React Query mutation选项
 * @returns useMutation结果
 */
export function useHttpDelete<TData = any>(
  options?: UseMutationOptions<TData, Error, { url: string; config?: AxiosRequestConfig }>
) {
  return useMutation({
    mutationFn: ({ url, config }) => request.delete<any, TData>(url, config),
    ...options,
  });
}

/**
 * 通用HTTP请求Hook
 * @param options React Query mutation选项
 * @returns useMutation结果
 */
export function useHttpRequest<TData = any, TVariables = any>(
  options?: UseMutationOptions<TData, Error, AxiosRequestConfig & { data?: TVariables }>
) {
  return useMutation({
    mutationFn: (config) => request(config),
    ...options,
  });
}

/**
 * 使用查询客户端Hook
 * @returns QueryClient实例
 */
export function useHttpQueryClient() {
  return useQueryClient();
}