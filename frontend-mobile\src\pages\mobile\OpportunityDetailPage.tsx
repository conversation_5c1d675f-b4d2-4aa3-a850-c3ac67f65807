import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft,
  Target, 
  Building,
  User,
  Phone,
  Mail,
  DollarSign,
  Calendar,
  TrendingUp,
  Clock,
  MessageSquare,
  FileText,
  Tag
} from "lucide-react";

export default function OpportunityDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // 模拟商机详情数据（基于SQL样本数据）
  const opportunityDetail = {
    id: parseInt(id || "1"),
    opportunityCode: "OPP-2024-001",
    opportunityName: "智慧园区管理系统项目",
    opportunityType: "new_customer",
    opportunityTypeText: "新客户开发",
    opportunitySource: "exhibition",
    opportunitySourceText: "展会",
    partnerName: "北京卓越客户集团",
    contactPerson: "周八",
    contactPhone: "13700137000",
    contactEmail: "<EMAIL>",
    opportunityStage: "negotiation",
    opportunityStageText: "谈判阶段", 
    winProbability: 80,
    estimatedAmount: "¥2,800,000",
    estimatedCloseDate: "2024-08-15",
    actualCloseDate: null,
    opportunityStatus: "active",
    opportunityStatusText: "进行中",
    priority: "high",
    priorityText: "高",
    productInterest: "智慧园区管理平台,物联网设备集成",
    requirements: "需要一套完整的园区智能化管理系统，包含访客管理、设备监控、能耗管理等功能",
    competitionInfo: "主要竞争对手：华为、海康威视",
    nextAction: "准备最终报价方案，下周三进行技术方案演示",
    nextFollowDate: "2024-07-18",
    responsibleUserName: "张经理",
    deptName: "销售部",
    tags: "大客户,长期合作",
    remark: "北京卓越客户集团的重点项目，预计Q3签约",
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-07-16 14:20:00"
  };

  // 跟进记录数据
  const followRecords = [
    {
      id: 1,
      followType: "phone",
      followTypeText: "电话跟进",
      followDate: "2024-07-01 09:30:00",
      followDuration: 45,
      contactPerson: "周八",
      followContent: "电话沟通项目需求，了解具体功能要求和预算范围。客户对我们的技术方案很感兴趣，希望看到详细的技术架构图。",
      followResult: "positive",
      followResultText: "积极响应",
      nextAction: "准备技术方案PPT，安排下周现场演示",
      nextFollowDate: "2024-07-08",
      followUserName: "张经理",
      remark: "客户态度积极，项目推进顺利"
    },
    {
      id: 2,
      followType: "visit",
      followTypeText: "上门拜访",
      followDate: "2024-07-08 14:00:00",
      followDuration: 120,
      contactPerson: "周八",
      followContent: "现场拜访，进行技术方案演示。展示了类似项目案例，客户对技术实力认可。讨论了项目时间节点和人员配置。",
      followResult: "interested",
      followResultText: "有兴趣",
      nextAction: "根据反馈优化方案，准备商务报价",
      nextFollowDate: "2024-07-15",
      followUserName: "张经理",
      remark: "现场演示效果良好，客户专业团队参与讨论"
    },
    {
      id: 3,
      followType: "email",
      followTypeText: "邮件跟进",
      followDate: "2024-07-15 16:20:00",
      followDuration: 30,
      contactPerson: "周八",
      followContent: "发送正式商务报价单和项目实施计划。价格在客户预算范围内，实施周期6个月。",
      followResult: "considering",
      followResultText: "考虑中",
      nextAction: "等待客户内部评审，准备最终谈判",
      nextFollowDate: "2024-07-18",
      followUserName: "张经理",
      remark: "报价已发送，等待客户反馈"
    }
  ];

  const getStageColor = (stage: string) => {
    switch (stage) {
      case "initial": return "secondary";
      case "interested": return "warning"; 
      case "proposal": return "accent";
      case "negotiation": return "primary";
      case "closed_won": return "success";
      case "closed_lost": return "destructive";
      default: return "secondary";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "destructive";
      case "medium": return "warning";
      case "low": return "secondary";
      default: return "secondary";
    }
  };

  const getFollowTypeColor = (type: string) => {
    switch (type) {
      case "phone": return "primary";
      case "email": return "success";
      case "visit": return "info";
      case "wechat": return "warning";
      case "meeting": return "accent";
      default: return "secondary";
    }
  };

  const getFollowResultColor = (result: string) => {
    switch (result) {
      case "positive": return "success";
      case "interested": return "primary";
      case "considering": return "info";
      case "need_more_info": return "warning";
      case "price_concern": return "destructive";
      case "no_response": return "secondary";
      case "rejected": return "destructive";
      default: return "secondary";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <MobileHeader 
        title="商机详情" 
        showBack 
        onBack={() => navigate(-1)}
      />
      
      <div className="p-4 space-y-4">
        {/* 商机基本信息卡片 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Target className="h-5 w-5 text-primary" />
                <h1 className="text-lg font-bold">{opportunityDetail.opportunityName}</h1>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                编号: {opportunityDetail.opportunityCode}
              </p>
              <div className="flex items-center gap-2 mb-3">
                <Badge variant={getStageColor(opportunityDetail.opportunityStage) as any}>
                  {opportunityDetail.opportunityStageText}
                </Badge>
                <Badge variant={getPriorityColor(opportunityDetail.priority) as any}>
                  {opportunityDetail.priorityText}优先级
                </Badge>
                <Badge variant="outline">
                  {opportunityDetail.opportunityStatusText}
                </Badge>
              </div>
            </div>
          </div>

          {/* 成单概率 */}
          <div className="mb-4">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="flex items-center gap-1 text-muted-foreground">
                <TrendingUp className="h-4 w-4" />
                成单概率
              </span>
              <span className="font-bold text-lg">{opportunityDetail.winProbability}%</span>
            </div>
            <Progress value={opportunityDetail.winProbability} className="h-3" />
          </div>

          {/* 金额和时间 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-xs text-muted-foreground">预估金额</p>
                <p className="font-bold text-primary">{opportunityDetail.estimatedAmount}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-xs text-muted-foreground">预计成交</p>
                <p className="font-medium">{opportunityDetail.estimatedCloseDate}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 客户信息卡片 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Building className="h-4 w-4" />
            客户信息
          </h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-muted-foreground">客户名称</p>
              <p className="font-medium">{opportunityDetail.partnerName}</p>
            </div>
            <div className="grid grid-cols-1 gap-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">联系人</p>
                  <p className="font-medium">{opportunityDetail.contactPerson}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">联系电话</p>
                  <p className="font-medium">{opportunityDetail.contactPhone}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">邮箱</p>
                  <p className="font-medium">{opportunityDetail.contactEmail}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 标签页内容 */}
        <Tabs defaultValue="details" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">详细信息</TabsTrigger>
            <TabsTrigger value="follow">跟进记录</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            {/* 商机详情 */}
            <div className="bg-card rounded-lg p-4 shadow-soft">
              <h3 className="font-semibold mb-3">商机详情</h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">商机类型</p>
                  <p className="font-medium">{opportunityDetail.opportunityTypeText}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">商机来源</p>
                  <p className="font-medium">{opportunityDetail.opportunitySourceText}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">感兴趣的产品/服务</p>
                  <p className="font-medium">{opportunityDetail.productInterest}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">客户需求</p>
                  <p className="font-medium text-sm leading-relaxed">{opportunityDetail.requirements}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">竞争对手信息</p>
                  <p className="font-medium">{opportunityDetail.competitionInfo}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">下一步行动</p>
                  <p className="font-medium">{opportunityDetail.nextAction}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">下次跟进日期</p>
                  <p className="font-medium">{opportunityDetail.nextFollowDate}</p>
                </div>
                {opportunityDetail.tags && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">标签</p>
                    <div className="flex flex-wrap gap-1">
                      {opportunityDetail.tags.split(',').map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag.trim()}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                {opportunityDetail.remark && (
                  <div>
                    <p className="text-sm text-muted-foreground">备注</p>
                    <p className="font-medium text-sm leading-relaxed">{opportunityDetail.remark}</p>
                  </div>
                )}
              </div>
            </div>

            {/* 负责人信息 */}
            <div className="bg-card rounded-lg p-4 shadow-soft">
              <h3 className="font-semibold mb-3">负责人信息</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">负责人</p>
                  <p className="font-medium">{opportunityDetail.responsibleUserName}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">所属部门</p>
                  <p className="font-medium">{opportunityDetail.deptName}</p>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="follow" className="space-y-4">
            {/* 跟进记录列表 */}
            <div className="space-y-3">
              {followRecords.map((record) => (
                <div key={record.id} className="bg-card rounded-lg p-4 shadow-soft">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Badge variant={getFollowTypeColor(record.followType) as any} className="text-xs">
                        {record.followTypeText}
                      </Badge>
                      <Badge variant={getFollowResultColor(record.followResult) as any} className="text-xs">
                        {record.followResultText}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-muted-foreground">{record.followDate}</p>
                      {record.followDuration && (
                        <p className="text-xs text-muted-foreground">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {record.followDuration}分钟
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">联系人: {record.contactPerson}</span>
                      <span className="text-muted-foreground">跟进人: {record.followUserName}</span>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium mb-1">跟进内容：</p>
                      <p className="text-sm text-muted-foreground leading-relaxed">{record.followContent}</p>
                    </div>

                    {record.nextAction && (
                      <div>
                        <p className="text-sm font-medium mb-1">下一步行动：</p>
                        <p className="text-sm text-muted-foreground">{record.nextAction}</p>
                      </div>
                    )}

                    {record.nextFollowDate && (
                      <div className="flex items-center gap-1 text-sm">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">下次跟进: {record.nextFollowDate}</span>
                      </div>
                    )}

                    {record.remark && (
                      <div className="pt-2 border-t border-border">
                        <p className="text-xs text-muted-foreground">{record.remark}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

        </Tabs>
      </div>
    </div>
  );
}