import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  LogOut,
  ChevronRight,
  Phone,
  Mail,
  Building,
  Users
} from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function ProfilePage() {
  const navigate = useNavigate();
  
  const user = {
    name: "张经理",
    email: "<EMAIL>",
    phone: "138****8888",
    department: "销售部",
    role: "销售经理",
    avatar: ""
  };

  const menuItems = [
    {
      icon: Users,
      title: "伙伴管理",
      subtitle: "管理客户、供应商和合作伙伴",
      action: () => navigate("/partners")
    },
  ];

  return (
    <div className="min-h-screen bg-background pb-20">
      <MobileHeader title="个人中心" />
      
      <div className="p-4 space-y-6">
        {/* 用户信息卡片 */}
        <div className="bg-gradient-hero rounded-lg p-6 text-white">
          <div className="flex items-center gap-4 mb-4">
            <Avatar className="h-16 w-16 border-2 border-white/20">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="bg-white/20 text-white text-lg">
                头像
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-xl font-bold mb-1">{user.name}</h2>
              <p className="text-white/80 text-sm">{user.role}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-white/60" />
              <span className="text-white/80">{user.department}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-white/60" />
              <span className="text-white/80">{user.phone}</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-white/60" />
              <span className="text-white/80 truncate">{user.email}</span>
            </div>
          </div>
        </div>

        {/* 功能菜单 */}
        <div className="bg-card rounded-lg shadow-soft">
          {menuItems.map((item, index) => (
            <Button
              key={index}
              variant="ghost"
              className="w-full justify-start h-auto p-4 rounded-lg hover:bg-accent/50"
              onClick={item.action}
            >
              <div className="flex items-center gap-3 flex-1">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <item.icon className="h-4 w-4 text-primary" />
                </div>
                <div className="flex-1 text-left">
                  <p className="font-medium text-sm">{item.title}</p>
                  <p className="text-xs text-muted-foreground">{item.subtitle}</p>
                </div>
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              </div>
            </Button>
          ))}
        </div>

        {/* 退出登录 */}
        <div className="pt-4">
          <Button
            variant="outline"
            className="w-full text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
            onClick={() => navigate("/login")}
          >
            <LogOut className="h-4 w-4 mr-2" />
            退出登录
          </Button>
        </div>

        {/* 版本信息 */}
        <div className="text-center text-xs text-muted-foreground space-y-1">
          <p>探数者综合管理平台</p>
          <p>版本 v1.0.0</p>
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
}