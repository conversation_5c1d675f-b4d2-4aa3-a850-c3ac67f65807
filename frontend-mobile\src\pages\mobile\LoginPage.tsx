import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Lock,
  Shield,
  Eye,
  EyeOff,
  RefreshCw,
  Loader2,
} from "lucide-react";
import AuthAPI from "@/api/auth";
import { Auth } from "@/utils/auth";

interface LoginFormData {
  username: string;
  password: string;
  captchaCode: string;
  captchaKey: string;
  rememberMe: boolean;
}

export default function LoginPage() {
  const navigate = useNavigate();
  const location = useLocation();

  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [captchaLoading, setCaptchaLoading] = useState(false);
  const [captchaBase64, setCaptchaBase64] = useState("");
  const [isCapsLock, setIsCapsLock] = useState(false);

  const [formData, setFormData] = useState<LoginFormData>({
    username: "admin",
    password: "123456",
    captchaCode: "",
    captchaKey: "",
    rememberMe: false,
  });

  const [errors, setErrors] = useState<Partial<LoginFormData>>({});

  // 获取验证码
  const getCaptcha = async () => {
    setCaptchaLoading(true);
    try {
      // 模拟API调用
      const captchaData = await AuthAPI.getCaptcha();

      setFormData((prev) => ({ ...prev, captchaKey: captchaData.captchaKey }));
      setCaptchaBase64(captchaData.captchaBase64);
    } catch (error) {
      console.error("获取验证码失败:", error);
    } finally {
      setCaptchaLoading(false);
    }
  };

  // 表单验证
  const validateForm = () => {
    const newErrors: Partial<LoginFormData> = {};

    if (!formData.username.trim()) {
      newErrors.username = "请输入用户名";
    }

    if (!formData.password) {
      newErrors.password = "请输入密码";
    } else if (formData.password.length < 6) {
      newErrors.password = "密码至少需要6位";
    }

    if (!formData.captchaCode.trim()) {
      newErrors.captchaCode = "请输入验证码";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理登录提交
  const handleLogin = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // 模拟登录API调用
      const { accessToken, refreshToken } = await AuthAPI.login(formData);

      // 登录成功
      console.log("登录成功:", formData);
      // 保存token
      Auth.setTokens(accessToken, refreshToken, formData.rememberMe);

      // 获取重定向路径
      const from = (location.state as any)?.from?.pathname || "/";
      navigate(from, { replace: true });
    } catch (error) {
      console.error("登录失败:", error);
      getCaptcha(); // 刷新验证码
    } finally {
      setLoading(false);
    }
  };

  // 检查大写锁定
  const checkCapsLock = (event: React.KeyboardEvent) => {
    if (event.getModifierState) {
      setIsCapsLock(event.getModifierState("CapsLock"));
    }
  };

  // 处理输入变化
  const handleInputChange = (
    field: keyof LoginFormData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // 清除对应字段的错误信息
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  // 处理回车键登录
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleLogin();
    }
  };

  // 组件挂载时获取验证码
  useEffect(() => {
    getCaptcha();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex flex-col justify-start items-center p-4 pt-8">
      <div className="w-full max-w-md">
        {/* 头部标题 */}
        <div className="text-center mb-4">
          <div className="w-12 h-12 mx-auto mb-2">
            <img
              src="/logo.png"
              alt="探数者综合管理平台"
              className="w-full h-full object-contain"
            />
          </div>
          <h1 className="text-xl font-bold text-foreground mb-1">
            探数者综合管理平台
          </h1>
          <p className="text-sm text-muted-foreground">请登录您的账户</p>
        </div>

        {/* 登录表单 */}
        <div className="bg-card rounded-lg shadow-lg p-4 space-y-4">
          {/* 用户名 */}
          <div className="space-y-1">
            <Label htmlFor="username" className="text-sm">
              用户名
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="username"
                type="text"
                placeholder="请输入用户名"
                value={formData.username}
                onChange={(e) => handleInputChange("username", e.target.value)}
                className={`pl-10 ${
                  errors.username ? "border-destructive" : ""
                }`}
                onKeyPress={handleKeyPress}
              />
            </div>
            {errors.username && (
              <p className="text-sm text-destructive">{errors.username}</p>
            )}
          </div>

          {/* 密码 */}
          <div className="space-y-1">
            <Label htmlFor="password" className="text-sm">
              密码
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="请输入密码"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                onKeyUp={checkCapsLock}
                onKeyPress={handleKeyPress}
                className={`pl-10 pr-10 ${
                  errors.password ? "border-destructive" : ""
                }`}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {isCapsLock && (
              <p className="text-sm text-warning">大写锁定已开启</p>
            )}
            {errors.password && (
              <p className="text-sm text-destructive">{errors.password}</p>
            )}
          </div>

          {/* 验证码 */}
          <div className="space-y-1">
            <Label htmlFor="captcha" className="text-sm">
              验证码
            </Label>
            <div className="flex gap-3">
              <div className="flex-1 relative">
                <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="captcha"
                  type="text"
                  placeholder="请输入验证码"
                  value={formData.captchaCode}
                  onChange={(e) =>
                    handleInputChange("captchaCode", e.target.value)
                  }
                  className={`pl-10 ${
                    errors.captchaCode ? "border-destructive" : ""
                  }`}
                  onKeyPress={handleKeyPress}
                />
              </div>
              <div
                className="w-24 h-10 border rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity flex items-center justify-center bg-muted"
                onClick={getCaptcha}
              >
                {captchaLoading ? (
                  <RefreshCw className="h-4 w-4 text-muted-foreground animate-spin" />
                ) : captchaBase64 ? (
                  <img
                    src={captchaBase64}
                    alt="验证码"
                    className="w-full h-full object-fill"
                  />
                ) : (
                  <span className="text-xs text-muted-foreground">
                    点击刷新
                  </span>
                )}
              </div>
            </div>
            {errors.captchaCode && (
              <p className="text-sm text-destructive">{errors.captchaCode}</p>
            )}
          </div>

          {/* 记住我和忘记密码 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={formData.rememberMe}
                onCheckedChange={(checked) =>
                  handleInputChange("rememberMe", !!checked)
                }
              />
              <Label htmlFor="remember" className="text-sm font-normal">
                记住我
              </Label>
            </div>
            <button
              type="button"
              className="text-sm text-primary hover:underline"
              onClick={() => console.log("忘记密码")}
            >
              忘记密码？
            </button>
          </div>

          {/* 登录按钮 */}
          <Button onClick={handleLogin} disabled={loading} className="w-full">
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? "登录中..." : "登录"}
          </Button>

          {/* 其他登录方式 */}
          <div className="space-y-3">
            <div className="relative">
              <Separator />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="bg-card px-2 text-xs text-muted-foreground">
                  其他登录方式
                </span>
              </div>
            </div>

            <Button
              variant="outline"
              className="w-full"
              onClick={() => console.log("OAuth登录")}
            >
              第三方登录
            </Button>
          </div>
        </div>

        {/* 版本信息 */}
        <div className="text-center mt-3 text-xs text-muted-foreground">
          <p>探数者综合管理平台 v1.0.0</p>
        </div>
      </div>
    </div>
  );
}
