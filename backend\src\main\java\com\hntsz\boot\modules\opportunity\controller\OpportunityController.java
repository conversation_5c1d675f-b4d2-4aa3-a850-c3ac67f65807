package com.hntsz.boot.modules.opportunity.controller;

import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityForm;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO;
import com.hntsz.boot.modules.opportunity.service.OpportunityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 商机线索控制器
 *
 * <AUTHOR>
 */
@Tag(name = "商机线索管理")
@RestController
@RequestMapping("/api/v1/opportunities")
@RequiredArgsConstructor
@Validated
public class OpportunityController {

    private final OpportunityService opportunityService;

    @Operation(summary = "分页查询商机线索")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPerm('opportunity:list:query')")
    public PageResult<OpportunityVO> getOpportunityPage(OpportunityQuery queryParams) {
        return opportunityService.getOpportunityPage(queryParams);
    }

    @Operation(summary = "获取商机线索详情")
    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('opportunity:list:query')")
    public Result<OpportunityVO> getOpportunityDetail(
            @Parameter(description = "商机ID") @PathVariable Long id) {
        OpportunityVO opportunity = opportunityService.getOpportunityById(id);
        return Result.success(opportunity);
    }

    @Operation(summary = "获取商机表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('opportunity:list:edit')")
    public Result<OpportunityForm> getOpportunityFormData(
            @Parameter(description = "商机ID") @PathVariable Long id) {
        OpportunityForm formData = opportunityService.getOpportunityFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "新增商机线索")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('opportunity:list:add')")
    public Result<Void> createOpportunity(@Valid @RequestBody OpportunityForm formData) {
        boolean result = opportunityService.saveOpportunity(formData);
        return Result.judge(result);
    }

    @Operation(summary = "修改商机线索")
    @PutMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('opportunity:list:edit')")
    public Result<Void> updateOpportunity(
            @Parameter(description = "商机ID") @PathVariable Long id,
            @Valid @RequestBody OpportunityForm formData) {
        boolean result = opportunityService.updateOpportunity(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "归档商机线索")
    @PutMapping("/{ids}/archive")
    @PreAuthorize("@ss.hasPerm('opportunity:list:archive')")
    public Result<Void> archiveOpportunities(
            @Parameter(description = "商机ID，多个以英文逗号(,)分割") @PathVariable String ids,
            @Parameter(description = "归档原因") @RequestParam String archiveReason) {
        boolean result = opportunityService.archiveOpportunities(ids, archiveReason);
        return Result.judge(result);
    }

    @Operation(summary = "获取商机选项列表")
    @GetMapping("/options")
    public Result<List<Option<Long>>> getOpportunityOptions() {
        List<Option<Long>> options = opportunityService.getOpportunityOptions();
        return Result.success(options);
    }

    @Operation(summary = "生成商机编码")
    @GetMapping("/generate-code")
    public Result<String> generateOpportunityCode() {
        String code = opportunityService.generateOpportunityCode();
        return Result.success(code);
    }

    @Operation(summary = "批量转移商机负责人")
    @PutMapping("/transfer")
    @PreAuthorize("@ss.hasPerm('opportunity:list:transfer')")
    public Result<Void> transferOpportunities(
            @Parameter(description = "商机ID列表") @RequestParam List<Long> opportunityIds,
            @Parameter(description = "新负责人ID") @RequestParam Long responsibleUserId) {
        boolean result = opportunityService.transferOpportunities(opportunityIds, responsibleUserId);
        return Result.judge(result);
    }

    @Operation(summary = "更新商机阶段")
    @PutMapping("/{id}/stage")
    @PreAuthorize("@ss.hasPerm('opportunity:list:edit')")
    public Result<Void> updateOpportunityStage(
            @Parameter(description = "商机ID") @PathVariable Long id,
            @Parameter(description = "商机阶段") @RequestParam String opportunityStage,
            @Parameter(description = "成单概率") @RequestParam(required = false) Integer winProbability) {
        boolean result = opportunityService.updateOpportunityStage(id, opportunityStage, winProbability);
        return Result.judge(result);
    }

    @Operation(summary = "成交商机")
    @PutMapping("/{id}/win")
    @PreAuthorize("@ss.hasPerm('opportunity:list:edit')")
    public Result<Void> winOpportunity(
            @Parameter(description = "商机ID") @PathVariable Long id) {
        boolean result = opportunityService.winOpportunity(id);
        return Result.judge(result);
    }

    @Operation(summary = "失败商机")
    @PutMapping("/{id}/lose")
    @PreAuthorize("@ss.hasPerm('opportunity:list:edit')")
    public Result<Void> loseOpportunity(
            @Parameter(description = "商机ID") @PathVariable Long id,
            @Parameter(description = "失败原因") @RequestParam String lostReason) {
        boolean result = opportunityService.loseOpportunity(id, lostReason);
        return Result.judge(result);
    }
}