package com.hntsz.boot.modules.opportunity.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.modules.opportunity.converter.OpportunityConverter;
import com.hntsz.boot.modules.opportunity.mapper.OpportunityMapper;
import com.hntsz.boot.modules.opportunity.model.entity.Opportunity;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityForm;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO;
import com.hntsz.boot.modules.opportunity.service.OpportunityService;
import com.hntsz.boot.system.enums.DictCodeEnum;
import com.hntsz.boot.system.service.DictItemService;
import com.hntsz.boot.core.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商机线索服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpportunityServiceImpl extends ServiceImpl<OpportunityMapper, Opportunity> implements OpportunityService {

    private final OpportunityMapper opportunityMapper;
    private final OpportunityConverter opportunityConverter;
    private final DictItemService dictItemService;

    @Override
    public PageResult<OpportunityVO> getOpportunityPage(OpportunityQuery queryParams) {
        // 分页查询
        Page<OpportunityVO> page = opportunityMapper.selectOpportunityPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );

        // 填充字典标签
        fillDictLabels(page.getRecords());

        return PageResult.success(page);
    }

    @Override
    public OpportunityVO getOpportunityById(Long id) {
        OpportunityVO opportunityVO = opportunityMapper.selectOpportunityById(id);
        if (opportunityVO != null) {
            fillDictLabels(List.of(opportunityVO));
        }
        return opportunityVO;
    }

    @Override
    public OpportunityForm getOpportunityFormData(Long id) {
        Opportunity entity = this.getById(id);
        if (entity == null) {
            return new OpportunityForm();
        }
        return opportunityConverter.entityToForm(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOpportunity(OpportunityForm formData) {
        Opportunity entity = opportunityConverter.formToEntity(formData);
        
        // 自动生成商机编码
        if (StrUtil.isBlank(entity.getOpportunityCode())) {
            entity.setOpportunityCode(generateOpportunityCode());
        }
        
        // 设置默认值
        if (StrUtil.isBlank(entity.getOpportunityStage())) {
            entity.setOpportunityStage("initial");
        }
        if (StrUtil.isBlank(entity.getOpportunityStatus())) {
            entity.setOpportunityStatus("active");
        }
        if (entity.getWinProbability() == null) {
            entity.setWinProbability(0);
        }
        if (StrUtil.isBlank(entity.getPriority())) {
            entity.setPriority("medium");
        }

        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOpportunity(Long id, OpportunityForm formData) {
        Opportunity entity = opportunityConverter.formToEntity(formData);
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOpportunities(String ids) {
        if (StrUtil.isBlank(ids)) {
            return false;
        }
        
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        return this.removeByIds(idList);
    }

    @Override
    public List<Option<Long>> getOpportunityOptions() {
        List<OpportunityVO> opportunities = opportunityMapper.selectOpportunityOptions();
        return opportunities.stream()
                .map(opportunity -> new Option<>(opportunity.getId(), opportunity.getOpportunityName()))
                .collect(Collectors.toList());
    }

    @Override
    public String generateOpportunityCode() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "OPP-" + dateStr + "-";
        
        // 查询当天已有的商机数量
        LambdaQueryWrapper<Opportunity> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(Opportunity::getOpportunityCode, prefix);
        long count = this.count(wrapper);
        
        return prefix + String.format("%03d", count + 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean transferOpportunities(List<Long> opportunityIds, Long responsibleUserId) {
        if (CollUtil.isEmpty(opportunityIds) || responsibleUserId == null) {
            return false;
        }
        
        int updated = opportunityMapper.batchUpdateResponsibleUser(opportunityIds, responsibleUserId, SecurityUtils.getUserId());
        return updated > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOpportunityStage(Long id, String opportunityStage, Integer winProbability) {
        Opportunity opportunity = this.getById(id);
        if (opportunity == null) {
            return false;
        }
        
        opportunity.setOpportunityStage(opportunityStage);
        if (winProbability != null) {
            opportunity.setWinProbability(winProbability);
        }
        
        return this.updateById(opportunity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean winOpportunity(Long id) {
        Opportunity opportunity = this.getById(id);
        if (opportunity == null) {
            return false;
        }
        
        opportunity.setOpportunityStage("closed_won");
        opportunity.setOpportunityStatus("won");
        opportunity.setWinProbability(100);
        opportunity.setActualCloseDate(LocalDate.now());
        
        return this.updateById(opportunity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean loseOpportunity(Long id, String lostReason) {
        Opportunity opportunity = this.getById(id);
        if (opportunity == null) {
            return false;
        }
        
        opportunity.setOpportunityStage("closed_lost");
        opportunity.setOpportunityStatus("lost");
        opportunity.setWinProbability(0);
        opportunity.setLostReason(lostReason);
        opportunity.setActualCloseDate(LocalDate.now());
        
        return this.updateById(opportunity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean archiveOpportunities(String ids, String archiveReason) {
        if (StrUtil.isBlank(ids) || StrUtil.isBlank(archiveReason)) {
            return false;
        }
        
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        // 验证商机状态，只有已失败和已取消的商机才能归档
        List<Opportunity> opportunities = this.listByIds(idList);
        for (Opportunity opportunity : opportunities) {
            if (!"lost".equals(opportunity.getOpportunityStatus()) && 
                !"cancelled".equals(opportunity.getOpportunityStatus())) {
                throw new RuntimeException("只有已失败或已取消的商机才能归档");
            }
        }
        
        // 批量更新商机状态为已归档，并设置归档原因
        return this.lambdaUpdate()
                .set(Opportunity::getOpportunityStatus, "archived")
                .set(Opportunity::getArchiveReason, archiveReason)
                .in(Opportunity::getId, idList)
                .update();
    }

    /**
     * 填充字典标签
     *
     * @param opportunities 商机列表
     */
    private void fillDictLabels(List<OpportunityVO> opportunities) {
        if (CollUtil.isEmpty(opportunities)) {
            return;
        }

        // 获取字典数据
        Map<String, String> opportunityTypeMap = dictItemService.getDictItems(DictCodeEnum.OPPORTUNITY_TYPE.getValue())
                .stream().collect(Collectors.toMap(item -> item.getValue(), item -> item.getLabel(), (existing, replacement) -> existing));
        Map<String, String> opportunitySourceMap = dictItemService.getDictItems(DictCodeEnum.OPPORTUNITY_SOURCE.getValue())
                .stream().collect(Collectors.toMap(item -> item.getValue(), item -> item.getLabel(), (existing, replacement) -> existing));
        Map<String, String> lostReasonMap = dictItemService.getDictItems(DictCodeEnum.LOST_REASON.getValue())
                .stream().collect(Collectors.toMap(item -> item.getValue(), item -> item.getLabel(), (existing, replacement) -> existing));

        // 商机阶段映射
        Map<String, String> stageMap = Map.of(
                "initial", "初步接触",
                "interested", "有意向",
                "proposal", "方案阶段",
                "negotiation", "谈判阶段",
                "closed_won", "成交",
                "closed_lost", "失败"
        );

        // 商机状态映射
        Map<String, String> statusMap = Map.of(
                "active", "进行中",
                "won", "已成交",
                "lost", "已失败",
                "cancelled", "已取消",
                "archived", "已归档"
        );

        // 优先级映射
        Map<String, String> priorityMap = Map.of(
                "high", "高",
                "medium", "中",
                "low", "低"
        );

        // 填充标签
        for (OpportunityVO opportunity : opportunities) {
            opportunity.setOpportunityTypeLabel(opportunityTypeMap.get(opportunity.getOpportunityType()));
            opportunity.setOpportunitySourceLabel(opportunitySourceMap.get(opportunity.getOpportunitySource()));
            opportunity.setOpportunityStageLabel(stageMap.get(opportunity.getOpportunityStage()));
            opportunity.setOpportunityStatusLabel(statusMap.get(opportunity.getOpportunityStatus()));
            opportunity.setPriorityLabel(priorityMap.get(opportunity.getPriority()));
            opportunity.setLostReasonLabel(lostReasonMap.get(opportunity.getLostReason()));
        }
    }

}