import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { StatsCard } from "@/components/mobile/StatsCard";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { 
  FileText, 
  Target, 
  Users, 
  DollarSign, 
  TrendingUp,
  CheckCircle
} from "lucide-react";

export default function MobileIndex() {
  const navigate = useNavigate();
  
  const user = {
    name: "张经理",
    email: "<EMAIL>",
    phone: "138****8888",
    department: "销售部",
    role: "销售经理",
    avatar: ""
  };
  
  const statsData = [
    {
      title: "商机总数",
      value: "56",
      icon: Target,
      gradient: true,
      onClick: () => navigate("/opportunities")
    },
    {
      title: "合同总数",
      value: "128",
      icon: FileText,
      onClick: () => navigate("/contracts")
    },
  ];

  return (
    <div className="min-h-screen bg-background pb-20">
      <MobileHeader title="探数者综合管理平台" />
      
      <div className="p-4 space-y-6">
        {/* 欢迎区域 */}
        <div className="bg-gradient-hero rounded-lg p-6 text-white">
          <div className="flex items-center gap-4 mb-4">
            <Avatar className="h-12 w-12 border-2 border-white/20">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="bg-white/20 text-white text-base">
                头像
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-bold">早上好，{user.name}</h2>
              <p className="text-white/80 text-sm">{user.role}</p>
            </div>
          </div>
        </div>

        {/* 数据统计 */}
        <div>
          <h3 className="text-lg font-semibold mb-4">数据概览</h3>
          <div className="grid grid-cols-2 gap-4">
            {statsData.map((stat, index) => (
              <StatsCard
                key={index}
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                gradient={stat.gradient}
                onClick={stat.onClick}
              />
            ))}
          </div>
        </div>

        {/* 待跟进商机 */}
        <div>
          <h3 className="text-lg font-semibold mb-4">待跟进商机</h3>
          <div className="space-y-3">
            {[
              { 
                id: 1,
                opportunityCode: "OP2024001",
                opportunityName: "企业数字化转型方案", 
                partnerName: "中国移动通信集团", 
                contactPerson: "李经理",
                estimatedAmount: "¥1,200,000",
                nextFollowDate: "2024-01-25",
                followType: "visit", // 上门拜访
                followTypeText: "上门拜访",
                lastFollowResult: "interested", // 有兴趣
                lastFollowResultText: "有兴趣",
                priority: "high",
                priorityText: "高",
                opportunityStage: "proposal",
                opportunityStageText: "方案阶段",
                winProbability: 75,
                daysUntilFollow: 0, // 今天需要跟进
                isOverdue: false
              },
              { 
                id: 2,
                opportunityCode: "OP2024002",
                opportunityName: "云计算基础设施服务", 
                partnerName: "招商银行股份有限公司", 
                contactPerson: "王总监",
                estimatedAmount: "¥800,000",
                nextFollowDate: "2024-01-26",
                followType: "phone", // 电话跟进
                followTypeText: "电话跟进",
                lastFollowResult: "considering", // 考虑中
                lastFollowResultText: "考虑中",
                priority: "medium",
                priorityText: "中",
                opportunityStage: "negotiation",
                opportunityStageText: "谈判阶段", 
                winProbability: 60,
                daysUntilFollow: 1, // 明天跟进
                isOverdue: false
              },
              { 
                id: 3,
                opportunityCode: "OP2024003",
                opportunityName: "智能数据分析平台", 
                partnerName: "平安保险集团", 
                contactPerson: "张主任",
                estimatedAmount: "¥950,000",
                nextFollowDate: "2024-01-23",
                followType: "email", // 邮件跟进
                followTypeText: "邮件跟进",
                lastFollowResult: "need_more_info", // 需要更多信息
                lastFollowResultText: "需要更多信息",
                priority: "high",
                priorityText: "高",
                opportunityStage: "interested",
                opportunityStageText: "有意向",
                winProbability: 40,
                daysUntilFollow: -2, // 已逾期2天
                isOverdue: true
              }
            ].map((opportunity) => (
              <div 
                key={opportunity.id} 
                className={`bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow ${
                  opportunity.isOverdue ? 'border-l-4 border-destructive' : ''
                }`}
                onClick={() => {
                  navigate(`/opportunities/${opportunity.id}`);
                }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      opportunity.priority === "high" 
                        ? "bg-destructive/10 text-destructive" 
                        : opportunity.priority === "medium"
                        ? "bg-warning/10 text-warning"
                        : "bg-secondary/10 text-secondary-foreground"
                    }`}>
                      {opportunity.priorityText}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      opportunity.opportunityStage === "proposal" 
                        ? "bg-warning/10 text-warning"
                        : opportunity.opportunityStage === "negotiation"
                        ? "bg-primary/10 text-primary"
                        : "bg-info/10 text-info"
                    }`}>
                      {opportunity.opportunityStageText}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className={`text-xs font-medium ${
                      opportunity.isOverdue 
                        ? "text-destructive" 
                        : opportunity.daysUntilFollow === 0
                        ? "text-warning"
                        : "text-muted-foreground"
                    }`}>
                      {opportunity.isOverdue 
                        ? `逾期${Math.abs(opportunity.daysUntilFollow)}天`
                        : opportunity.daysUntilFollow === 0
                        ? "今日跟进"
                        : `${opportunity.daysUntilFollow}天后`
                      }
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      成单率: {opportunity.winProbability}%
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div>
                    <h4 className="font-semibold text-sm mb-1">{opportunity.opportunityName}</h4>
                    <p className="text-xs text-muted-foreground">编号: {opportunity.opportunityCode}</p>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">{opportunity.partnerName}</span>
                    </div>
                    <span className="font-medium text-primary">{opportunity.estimatedAmount}</span>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-4">
                      <span className="text-muted-foreground">
                        联系人: {opportunity.contactPerson}
                      </span>
                      <span className={`px-2 py-1 rounded-full ${
                        opportunity.followType === "visit" 
                          ? "bg-info/10 text-info"
                          : opportunity.followType === "phone"
                          ? "bg-primary/10 text-primary"
                          : "bg-success/10 text-success"
                      }`}>
                        {opportunity.followTypeText}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs pt-2 border-t border-border">
                    <span className="text-muted-foreground">
                      上次结果: <span className={`${
                        opportunity.lastFollowResult === "interested" 
                          ? "text-primary font-medium"
                          : opportunity.lastFollowResult === "considering"
                          ? "text-info font-medium"
                          : "text-warning font-medium"
                      }`}>
                        {opportunity.lastFollowResultText}
                      </span>
                    </span>
                    <span className="text-muted-foreground">
                      {opportunity.nextFollowDate}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
}